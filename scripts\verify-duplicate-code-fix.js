#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify that the duplicate project code fix is properly implemented
 * This script checks the key files to ensure error handling is in place
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Duplicate Project Code Fix...\n');

// Files to check
const filesToCheck = [
  {
    path: 'src/app/api/projects/create-with-billing/route.ts',
    checks: [
      {
        name: 'Duplicate code error detection',
        pattern: /duplicate key value violates unique constraint "projects_code_key"/,
        required: true
      },
      {
        name: 'Specific error response for duplicates',
        pattern: /errorType:\s*['"]DUPLICATE_CODE['"]/,
        required: true
      },
      {
        name: 'Conflict status code (409)',
        pattern: /status:\s*409/,
        required: true
      }
    ]
  },
  {
    path: 'src/features/projects/utils/project-utils.ts',
    checks: [
      {
        name: 'generateUniqueProjectCode function',
        pattern: /export\s+const\s+generateUniqueProjectCode/,
        required: true
      },
      {
        name: 'Date-based uniqueness',
        pattern: /getFullYear|getMonth|getDate/,
        required: true
      },
      {
        name: 'Random suffix for uniqueness',
        pattern: /Math\.floor\(Math\.random\(\)/,
        required: true
      }
    ]
  },
  {
    path: 'src/features/billing/hooks/useProjectCreation.ts',
    checks: [
      {
        name: 'DUPLICATE_CODE error type handling',
        pattern: /errorData\.errorType\s*===\s*['"]DUPLICATE_CODE['"]/,
        required: true
      }
    ]
  },
  {
    path: 'src/features/projects/components/project-details-step.tsx',
    checks: [
      {
        name: 'RefreshCw icon import',
        pattern: /RefreshCw/,
        required: true
      },
      {
        name: 'Generate unique code button',
        pattern: /generateUniqueProjectCode/,
        required: true
      },
      {
        name: 'User guidance text',
        pattern: /duplicate code.*error.*refresh button/i,
        required: true
      }
    ]
  },
  {
    path: 'src/features/billing/services/project-integration.service.ts',
    checks: [
      {
        name: 'Automatic retry on duplicate code',
        pattern: /duplicate key value violates unique constraint.*projects_code_key/,
        required: true
      },
      {
        name: 'Dynamic import of utility function',
        pattern: /generateUniqueProjectCode.*=.*await import/,
        required: true
      }
    ]
  }
];

let allChecksPassed = true;
let totalChecks = 0;
let passedChecks = 0;

for (const file of filesToCheck) {
  console.log(`📁 Checking ${file.path}...`);
  
  if (!fs.existsSync(file.path)) {
    console.log(`   ❌ File not found: ${file.path}`);
    allChecksPassed = false;
    continue;
  }
  
  const content = fs.readFileSync(file.path, 'utf8');
  
  for (const check of file.checks) {
    totalChecks++;
    const found = check.pattern.test(content);
    
    if (check.required) {
      if (found) {
        console.log(`   ✅ ${check.name} - Found`);
        passedChecks++;
      } else {
        console.log(`   ❌ ${check.name} - Missing`);
        allChecksPassed = false;
      }
    }
  }
  
  console.log('');
}

// Test the utility function
console.log('🧪 Testing generateUniqueProjectCode function...');
try {
  // Import and test the function
  const utilsPath = path.resolve('src/features/projects/utils/project-utils.ts');
  if (fs.existsSync(utilsPath)) {
    const utilsContent = fs.readFileSync(utilsPath, 'utf8');
    
    // Check if function exists and has proper structure
    if (utilsContent.includes('generateUniqueProjectCode')) {
      console.log('   ✅ Function exists in utils file');
      
      // Check format components
      if (utilsContent.includes('nameAbbr') && utilsContent.includes('locationAbbr')) {
        console.log('   ✅ Uses name and location abbreviations');
      }
      
      if (utilsContent.includes('getFullYear') && utilsContent.includes('getMonth')) {
        console.log('   ✅ Uses date components for uniqueness');
      }
      
      if (utilsContent.includes('Math.random')) {
        console.log('   ✅ Uses random suffix for additional uniqueness');
      }
      
      passedChecks += 3;
    } else {
      console.log('   ❌ Function not found in utils file');
      allChecksPassed = false;
    }
    totalChecks += 3;
  }
} catch (error) {
  console.log(`   ❌ Error testing utility function: ${error.message}`);
  allChecksPassed = false;
}

console.log('');

// Summary
console.log('📊 Summary:');
console.log(`   Total checks: ${totalChecks}`);
console.log(`   Passed: ${passedChecks}`);
console.log(`   Failed: ${totalChecks - passedChecks}`);
console.log('');

if (allChecksPassed) {
  console.log('🎉 All checks passed! The duplicate project code fix is properly implemented.');
  console.log('');
  console.log('✅ Key improvements verified:');
  console.log('   • Clear error messages for duplicate codes');
  console.log('   • Automatic retry with unique codes');
  console.log('   • User-friendly generate button in form');
  console.log('   • Robust error handling at multiple levels');
  console.log('   • Unique code generation with date and random components');
  console.log('');
  console.log('🔧 The duplicate code error has been successfully fixed!');
  process.exit(0);
} else {
  console.log('❌ Some checks failed. Please review the implementation.');
  console.log('');
  console.log('🚨 The duplicate code error fix may not be complete!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('   1. Review the failed checks above');
  console.log('   2. Ensure all required error handling is implemented');
  console.log('   3. Test with actual duplicate code scenarios');
  console.log('   4. Re-run this verification script');
  process.exit(1);
}
