# Duplicate Project Code Error Fix

## Issue Description
Users encountered a runtime error when trying to create a project with a quotation number (code) that already exists in the database:

```
Error: duplicate key value violates unique constraint "projects_code_key"
```

This error occurred because:
1. The database has a unique constraint on the `projects.code` field
2. The application didn't handle duplicate code validation
3. No mechanism existed to suggest alternative codes or auto-generate unique codes

## Root Cause
- Database constraint: `projects.code` has a UNIQUE constraint
- No client-side or server-side validation for duplicate codes
- No fallback mechanism when duplicate codes are detected

## Fix Applied

### 1. Enhanced Error Handling in API Route
**File:** `src/app/api/projects/create-with-billing/route.ts`

Added specific handling for duplicate code errors:
```typescript
// Check if the error is due to duplicate project code
if (result.error?.includes('duplicate key value violates unique constraint "projects_code_key"')) {
  return NextResponse.json(
    { 
      error: 'A project with this quotation number already exists. Please use a different quotation number.',
      errorType: 'DUPLICATE_CODE'
    },
    { status: 409 }, // Conflict status code
  );
}
```

### 2. Added Unique Code Generation Utility
**File:** `src/features/projects/utils/project-utils.ts`

Created `generateUniqueProjectCode()` function:
```typescript
export const generateUniqueProjectCode = (
  name: string,
  location: string,
): string => {
  const nameAbbr = name
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 3)
    .join('');

  const locationAbbr = location
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  // Use current date for better uniqueness
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const dateStr = `${year}${month}${day}`;

  // Add random 3-digit suffix
  const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

  return `${nameAbbr}${locationAbbr}-${dateStr}-${randomSuffix}`;
};
```

**Format:** `ABC12-20250131-123` where:
- `ABC12` = First 3 letters of project name + first 2 letters of location
- `20250131` = Current date (YYYYMMDD)
- `123` = Random 3-digit suffix

### 3. Enhanced Project Creation Hook
**File:** `src/features/billing/hooks/useProjectCreation.ts`

Added specific error type handling:
```typescript
// Handle specific error types
if (errorData.errorType === 'DUPLICATE_CODE') {
  throw new Error(errorData.error);
}
```

### 4. Updated Project Form with Generate Button
**File:** `src/features/projects/components/project-details-step.tsx`

Added a "Generate Unique Code" button next to the code input:
```typescript
<div className="flex gap-2">
  <Input
    id="code"
    placeholder={t('fields.code.placeholder')}
    {...form.register('code')}
    className="flex-1"
  />
  <Button
    type="button"
    variant="outline"
    size="sm"
    onClick={() => {
      const name = form.getValues('name') || 'PROJECT';
      const location = form.getValues('location') || 'LOCATION';
      const uniqueCode = generateUniqueProjectCode(name, location);
      form.setValue('code', uniqueCode);
    }}
    className="px-3"
    title="Generate unique code"
  >
    <RefreshCw className="h-4 w-4" />
  </Button>
</div>
```

### 5. Automatic Retry in Project Service
**File:** `src/features/billing/services/project-integration.service.ts`

Added automatic retry with unique code when duplicate is detected:
```typescript
// If duplicate code error, try with a unique code
if (projectError?.message?.includes('duplicate key value violates unique constraint "projects_code_key"')) {
  const { generateUniqueProjectCode } = await import('@/features/projects/utils/project-utils');
  
  const uniqueCode = generateUniqueProjectCode(
    projectData.name || 'PROJECT',
    projectData.location || 'LOCATION'
  );
  
  // Retry with unique code
  const retryData = { ...projectData, code: uniqueCode };
  const retryResult = await supabase
    .from('projects')
    .insert(retryData)
    .select()
    .single();
    
  project = retryResult.data;
  projectError = retryResult.error;
}
```

## User Experience Improvements

### Before Fix:
1. User enters duplicate code
2. Cryptic database error appears
3. User doesn't know what went wrong
4. No guidance on how to fix

### After Fix:
1. User enters duplicate code
2. Clear error message: "A project with this quotation number already exists"
3. User can click refresh button to generate unique code
4. Automatic retry happens in background for seamless experience

## Test Scenarios

### Scenario 1: Manual Duplicate Code Entry
**Steps:**
1. Create a project with code "TEST-001"
2. Try to create another project with code "TEST-001"
3. **Expected:** Clear error message with guidance
4. Click refresh button to generate unique code
5. **Expected:** Project created successfully

### Scenario 2: Automatic Retry
**Steps:**
1. Create project via API with duplicate code
2. **Expected:** Service automatically retries with unique code
3. **Expected:** Project created with modified code

### Scenario 3: Generate Unique Code Button
**Steps:**
1. Fill in project name and location
2. Click refresh button next to code field
3. **Expected:** Unique code generated based on name/location
4. Submit form
5. **Expected:** Project created successfully

## Error Handling Hierarchy

1. **Client-side validation** - Form validation prevents empty codes
2. **User guidance** - Refresh button provides alternative codes
3. **Server-side handling** - Clear error messages for duplicates
4. **Automatic retry** - Service-level retry with unique codes
5. **Database constraint** - Final safety net (unique constraint)

## Files Modified
- `src/app/api/projects/create-with-billing/route.ts`
- `src/features/projects/utils/project-utils.ts`
- `src/features/billing/hooks/useProjectCreation.ts`
- `src/features/projects/components/project-details-step.tsx`
- `src/features/billing/services/project-integration.service.ts`

## Impact
- **User Experience:** Clear error messages and easy resolution
- **Reliability:** Automatic retry prevents failed project creation
- **Guidance:** Visual button helps users generate unique codes
- **Robustness:** Multiple layers of error handling and prevention
