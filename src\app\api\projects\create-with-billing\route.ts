import { projectIntegrationService } from '@/features/billing/services';
import type { ProjectFormData } from '@/features/projects/types/project';
import { createServerSupabaseClient } from '@/lib/supabase-server';
import type { Database } from '@/types';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient();

    // Get the authenticated user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile to check contractor_id
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('contractor_id, user_role')
      .eq('id', user.id)
      .single();

    if (profileError || !userProfile?.contractor_id) {
      return NextResponse.json(
        { error: 'User profile not found or missing contractor ID' },
        { status: 400 },
      );
    }

    // Only contractors can create projects
    if (userProfile.user_role !== 'contractor') {
      return NextResponse.json(
        { error: 'Only contractors can create projects' },
        { status: 403 },
      );
    }

    const projectData: ProjectFormData = await request.json();
    console.log('🚀 ~ POST ~ projectData:', projectData);

    // Create project with billing integration
    const result = await projectIntegrationService.createProjectWithBilling({
      projectData: {
        name: projectData.name,
        code: projectData.code,
        agency_id: projectData.agency_id,
        location: projectData.location,
        state: projectData.state as
          | Database['public']['Enums']['state_code']
          | null,
        start_date: projectData.start_date,
        end_date: projectData.end_date,
        status: projectData.status,
        contractor_id: userProfile.contractor_id,
      },
      billingOptions: {
        autoActivate: false, // Require payment to activate
      },
    });

    if (result.error || !result.data) {
      // Check if the error is due to duplicate project code
      if (result.error?.includes('duplicate key value violates unique constraint "projects_code_key"')) {
        return NextResponse.json(
          {
            error: 'A project with this quotation number already exists. Please use a different quotation number.',
            errorType: 'DUPLICATE_CODE'
          },
          { status: 409 }, // Conflict status code
        );
      }

      return NextResponse.json(
        { error: result.error || 'Failed to create project with billing' },
        { status: 500 },
      );
    }

    const { project, pmaSubscriptions } = result.data;

    // Generate next steps for user
    const nextSteps = [
      'Complete payment to activate PMA access for each certificate',
      'Set up project team members',
      'Configure project maintenance schedules',
      'Upload initial project documents',
    ];

    return NextResponse.json({
      project,
      pmaSubscriptions,
      nextSteps,
    });
  } catch (error) {
    console.error('Error creating project with billing:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
